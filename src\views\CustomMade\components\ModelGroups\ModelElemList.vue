<template>
  <div class="modelelemlist" id="modelelemlistTourStep">
    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-container">
        <el-skeleton :rows="2" animated />
        <el-skeleton :rows="2" animated />
        <el-skeleton :rows="2" animated />
      </div>
      <!-- 数据内容 -->
      <TransitionGroup v-else name="list" :duration="3000" appear>
        <div v-for="(item,index) in modelElemListGroupData" :if="item===item" :key="item.modelElemListID+'+'+index" class="card mb-3 " @mouseover="addHoverClass" @mouseout="removeHoverClass" style="max-width: 540px;" @click="GoModelElemEvent(item,'modelElemListGroup')">
          <div class="row g-0" style="cursor: pointer;border-radius: 5px;">
            <div class="col-md-4" style="display: flex; align-items: center; justify-content: center;">
              <template v-if="item.modelElemListImg">
                <img :src="item.modelElemListImg" class="image multi-content" style="max-height: 100%; max-width: 100%; object-fit: contain;" />
              </template>
              <template v-else>
                <el-skeleton-item variant="image" style="width: 100%; height:100%" />
              </template>
            </div>
            <div class="col-md-8">
              <div class="card-body">
                <h5 class="card-title">{{item.modelElemListCode}} {{ item.modelElemListCodeName }}</h5>
                <p class="card-text">{{item.modelElemListDesc}}</p>
                <p class="card-text"><small class="text-muted">{{ item?.modelElemSelected?.modelElemCode }} {{ item?.modelElemSelected?.modelElemCodeName }}</small></p>
              </div>
            </div>
          </div>
        </div>
        <div v-if="modelElemListData&&modelElemListData.length>0">
          <el-collapse v-model="collapseAvtive" accordion v-loading="modelElemListLoading">
            <el-collapse-item name="1">
              <template #title="{ isActive }">
                <!-- <div :class="['title-wrapper', { 'is-active': isActive }]">
                <el-icon class="header-icon">
                  <info-filled />
                </el-icon>
              </div> -->
                <el-divider :key="isActive" id="moredesignsTourStep">{{ $t("custommade.modelelemlist.moredesigns","更多设计") }}</el-divider>
              </template>
              <div v-for="(item,index) in modelElemListData" :if="item.modelElemListIsClientShow" :key="item.modelElemListID+'+'+index" class="card mb-3 " @mouseover="addHoverClass" @mouseout="removeHoverClass" style="max-width: 540px;" @click="GoModelElemEvent(item,'modelElemList')">
                <div class="row g-0" style="cursor: pointer;border-radius: 5px;">
                  <div class="col-md-4" style="display: flex; align-items: center; justify-content: center;">
                    <template v-if="item.modelElemListImg">
                      <img :src="item.modelElemListImg" class="image multi-content" style="max-height: 100%; max-width: 100%; object-fit: contain;" />
                    </template>
                    <template v-else>
                      <el-skeleton-item variant="image" style="width: 100%; height:100%" />
                    </template>
                  </div>
                  <div class="col-md-8">
                    <div class="card-body">
                      <h5 class="card-title">{{item.modelElemListCode}} {{ item.modelElemListCodeName }}</h5>
                      <p class="card-text">{{item.modelElemListDesc}}</p>
                      <p class="card-text"><small class="text-muted">{{ item?.modelElemSelected?.modelElemCode }} {{ item?.modelElemSelected?.modelElemCodeName }}</small></p>
                    </div>
                  </div>
                </div>
              </div>
            </el-collapse-item>

          </el-collapse>
        </div>
      </TransitionGroup>
    </div>

    <!-- 底部固定按钮区域 -->
    <div class="container text-center sizebody mt-4">
      <div class="row justify-content-center">
        <div class="col">
          <el-button @click="goOther('SizeColumnAndBody')" id="SizeColumnAndBodyTourStep">规格/特体</el-button>
        </div>
        <!-- <div class="col">
          <el-button @click="goOther('SizeColumn')">规格</el-button>
        </div> -->
        <div class="col">
          <el-dropdown placement="top-start" @click="goOther('ClientPerson')" id="clientPersonTourStep">
            <el-button @click="goOther('ClientPerson')">
              <el-icon class="el-icon--right">
                <Operation />
              </el-icon>
              顾客
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="goOther('Address')">收货地址</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <!-- <el-button @click="goOther('ClientPerson')">顾客</el-button> -->
          <!-- <el-button @click="goOther('Address')">收货地址</el-button> -->
        </div>
        <div class="col">
          <el-dropdown placement="top-start" @click="submitOrderEvent" id="submitOrderTourStep">
            <el-button>
              <el-icon class="el-icon--right">
                <Operation />
              </el-icon>
              提交订单
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item><el-icon>
                    <ShoppingCart />
                  </el-icon>添加到购物车</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <!-- <el-button>添加购物车</el-button> -->
          <!-- <el-button>提交订单</el-button> -->
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import useCustomMadeStore from "@/store/custommade";
import { Operation, ShoppingCart } from '@element-plus/icons-vue'
export default {
  name: "ModelElemList",
  props: {
    groupname: {
      type: String,
      required: true,
    },
  },
  components: {
    Operation, ShoppingCart
  },
  data() {
    return {
      // collapseAvtive: '0',
      modelElemListGroupData: [],
      modelElemListData: [],
      activeComponentsStore: null,
      checkDataInterval: null,
      // 定义组件的响应式数据
    };
  },
  created() {
    // 在组件创建时初始化store
    this.activeComponentsStore = useCustomMadeStore();
  },
  mounted() {
    // 在组件挂载后尝试加载数据
    this.loadGroupData();
  },
  methods: {
    submitOrderEvent() {
      this.$emit('submitOrder');
    },
    loadGroupData() {
      if (!this.activeComponentsStore) {
        console.warn('❌ Store not initialized yet');
        return;
      }
      // 检查数据是否已加载
      if (!this.activeComponentsStore.isDataLoaded) {
        console.log('⏳ Data not loaded yet, waiting...');
        this.modelElemListGroupData = [];
        this.modelElemListData = [];
        return;
      }

      // 检查 orderData 是否存在
      if (!this.activeComponentsStore.groupData || this.activeComponentsStore.groupData.length === 0) {
        console.warn('❌ No order modelElemListGroupData available');
        this.modelElemListGroupData = [];
        this.modelElemListData = [];
        return;
      }

      // 根据groupname获取对应的数据
      const groupData = this.activeComponentsStore.getModelElemListGroupDataByCode(this.groupname);

      if (groupData && groupData.modelElemListGroup && groupData.modelElemListGroup.length > 0) {
        this.modelElemListGroupData = groupData.modelElemListGroup;
        this.modelElemListData = groupData.modelElemList;
      } else {
        // 如果没有找到数据，尝试通过groupName查找
        const groupDataByName = this.activeComponentsStore.getModelElemListGroupData(this.groupname);

        if (groupDataByName && groupDataByName.modelElemListGroup && groupDataByName.modelElemListGroup.length > 0) {
          this.modelElemListGroupData = groupDataByName.modelElemListGroup;
          this.modelElemListData = groupDataByName.modelElemList;
        } else {
          this.modelElemListGroupData = [];
          this.modelElemListData = [];
          return;
        }
      }
    },
    getGroupModelElemBaseData(groupname) {
      return this.activeComponentsStore.getModelElemListGroupData(groupname);
    },
    goOther(name) {
      this.activeComponentsStore.setactiveComponentName(name);
    },
    GoModelElemEvent(item, modelElem = "modelElemListGroup") {
      if (this.modelElemListLoading) {
        return
      }
      this.activeComponentsStore.setActiveModelElemListData(item, modelElem);
      this.activeComponentsStore.setactiveComponentName('ModelElem');
    },
    toggleClasses(element, classes, action) {
      classes.forEach(className => {
        element.classList[action](className);
      });
    },
    addHoverClass(event) {
      this.toggleClasses(event.currentTarget, ['border', 'border-1.5', 'border-secondary'], 'add');
    },
    removeHoverClass(event) {
      this.toggleClasses(event.currentTarget, ['border', 'border-1.5', 'border-secondary'], 'remove');
    }
  },
  computed: {
    collapseAvtive: {
      get() {
        return this.activeComponentsStore.collapseActiveState[this.groupname] || '0';
      },
      set(val) {
        this.activeComponentsStore.setCollapseActive(this.groupname, val);
      }
    },
    isLoading() {
      return !this.activeComponentsStore || this.activeComponentsStore.isLoading || !this.activeComponentsStore.isDataLoaded;
    },
    modelElemListLoading() {
      return this.activeComponentsStore.modelElemListLoading;
    }
  },

}
</script>

<style lang="scss">
.loading-container {
  padding: 20px;

  .el-skeleton {
    margin-bottom: 20px;
  }
}

.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.modelelemlist {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  // overflow-y: auto !important;
  position: relative;
  /* 为底部的 sticky 元素留出空间，防止内容被遮挡 */
  // padding-bottom: 80px;
}

// .content-area {
//   // padding-bottom: 20px;
// }

.sizebody {
  position: sticky;
  bottom: 0;
  z-index: 1000;
  padding: 16px 0;
  margin: 0;
  width: 100%;
  background-color: var(--el-bg-color-overlay, white);
  border-top: 1px solid #ebeef5;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}
</style>
