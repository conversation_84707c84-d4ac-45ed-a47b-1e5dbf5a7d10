<template>
  <div>
    <el-input class="mb-2" v-model="searchForm.text" style="width: 240px" placeholder="输入号型" @clear="seacrhEvent()" clearable>
      <template #append>
        <el-button @click="seacrhEvent()">
          <el-icon class="el-icon--right">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
              <path fill="currentColor" d="m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704"></path>
            </svg>
          </el-icon>
        </el-button>
      </template>
    </el-input>
    <div class="text-center" v-loading="sizeDataLoading">
      <el-space wrap>
        <div v-for="item in SizeData" :key="item.id">
          <el-button style="width: 78px;" :type="activeGroup.sizeID===item.id?'success':''" @click="SizeClickEvent(item)"> {{ item.code }}</el-button>
        </div>
      </el-space>
      <!-- <div class="row justify-content-start">
        <div class="col" v-for="item in SizeData" :key="item.id">
          <el-button :type="activeGroup.sizeID===item.id?'success':''" @click="SizeClickEvent(item)"> {{ item.code }}</button>
        </div>
      </div> -->
    </div>
    <div class="center align-items-center mb-2 mt-2">
      <el-pagination layout="prev, pager, next" :total="totalCount" :page-size="searchForm.maxResultCount" @current-change="handleCurrentChange" />
    </div>
  </div>
</template>

<script>
import useCustomMadeStore from "@/store/custommade";
import { Search } from "@element-plus/icons-vue";
export default {
  name: "Size",
  components: {
    Search,
  },
  props: {},
  data() {
    return {
      SizeData: [],
      totalCount: 0,
      sizeDataLoading: false,
      activeComponentsStore: useCustomMadeStore(),
      searchForm: {
        text: "",
        skipCount: 0,
        maxResultCount: 20,
        modelID: "",
        groupID: "",
      },
      api: {
        getSize: "/mtmshop/customMade/getSize",
      },
    };
  },
  computed: {
    activeGroup() {
      return this.activeComponentsStore.activeGroup;
    },
  },
  created() {
    this.loadData();
  },
  methods: {
    async loadData() {
      if (
        this.activeGroup.modelID === null ||
        this.activeGroup.modelID === ""
      ) {
        return;
      }
      this.sizeDataLoading = true;
      this.searchForm.modelID = this.activeGroup.modelID;
      this.searchForm.groupID = this.activeGroup.groupID;

      await this.$api
        .ActionRequest(this.api.getSize, this.searchForm)
        .then((res) => {
          if (res) {
            this.SizeData = res.items;
            this.totalCount = res.totalCount;
            this.sizeDataLoading = false;
          }
        })
        .catch((res) => {
          this.sizeDataLoading = false;
        });
    },
    SizeClickEvent(item) {
      this.$emit("getSizeData", item);
    },
    seacrhEvent() {
      this.searchForm.skipCount = 0;
      this.loadData();
    },
    handleCurrentChange(number) {
      this.searchForm.skipCount = (number - 1) * this.searchForm.maxResultCount;
      this.loadData();
    },
  },
};
</script>

<style>
</style>
