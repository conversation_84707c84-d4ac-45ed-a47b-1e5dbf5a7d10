<template>
  <el-tour v-model="tourOpen" @close="tourCloseEvent" @finish="tourCloseEvent" :target-area-clickable="false">
    <el-tour-step :target="groupSettingTarget" title="在这里选择你要定制的服装组合">
      <!-- <img style="width: 240px" src="https://element-plus.org/images/element-plus-logo.svg" alt="tour.png" /> -->
      <div>点击这里可以切换不同的服装组合设置。</div>
    </el-tour-step>
    <el-tour-step :target="ref2" title="选择面料" description="点击这里可以查看面料" />
    <el-tour-step :target="ref3" title="开始设计你的衣服" description="开始设计你的衣服" />
    <el-tour-step :target="ref1" title="更多设计" description="这里有更多的选项" />
    <el-tour-step :target="ref4" title="规格尺寸" description="选择你的规格尺寸" />
    <el-tour-step :target="ref5" title="顾客和地址" description="选择顾客和地址" />
    <el-tour-step :target="ref6" title="订单提交" description="最后提交你的订单" />
  </el-tour>
</template>

<script>
import useCustomMadeStore from "@/store/custommade.js";
export default {
  name: "Tour",
  props: {
    open: {
      type: Boolean,
      default: false,
    },

  },
  data() {
    const activeComponentsStore = useCustomMadeStore();
    return {
      activeComponentsStore: activeComponentsStore,
      tourOpen: false,
      groupSettingTarget: null,
      ref1: null,
      ref2: null,
      ref3: null,
      ref4: null,
      ref5: null,
    };
  },
  watch: {
    open(newVal) {
      this.tourOpen = newVal
    },
    'activeComponentsStore.activeGroup': {
      handler(newVal, oldVal) {
        if (newVal && oldVal) {
          this.initTargets();
        }
      },
      deep: true,
    },
  },
  computed: {
    // tourOpen() {
    //   return this.open
    // }
  },
  mounted() {
    this.initTargets();
  },
  created() {
    this.tourOpen = this.open;
    this.initTargets();
  },
  methods: {
    initTargets() {
      this.$nextTick(() => {
        this.groupSettingTarget = document.getElementById("groupSettingTourStep");
        this.ref1 = document.getElementById("moredesignsTourStep");
        this.ref2 = document.getElementById("tab-0");
        //this.ref2 = document.querySelector < HTMLElement > ('#fabricTourStep')
        this.ref3 = document.getElementById("modelelemlistTourStep");
        // this.ref3 = document.getElementById("modelGroupTourStep");
        this.ref4 = document.getElementById("SizeColumnAndBodyTourStep");
        this.ref5 = document.getElementById("clientPersonTourStep");
        this.ref6 = document.getElementById("submitOrderTourStep");
      })
    },
    tourCloseEvent() {
      this.tourOpen = false;
      this.$emit("tourCloseSuccess");
    },
  },
};
</script>

<style>
</style>