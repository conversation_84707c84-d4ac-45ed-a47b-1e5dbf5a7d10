<template>
  <div class="mb-4 box rounded-3 container  h-100">
    <KeepAlive>
      <div v-if="isLoading || !isDataLoaded" class="loading-container">
        <el-skeleton :rows="3" animated />
        <!-- <p class="loading-text">正在加载数据...</p> -->
      </div>
      <el-tabs v-else v-model="activeName" @tab-click="handleClick" class="p-2 h-100 modelgrouptabs">
        <TransitionGroup name="list" :duration="3000">
          <el-tab-pane :label="$t('custommade.modelgrouptabs.fabric')" key="0">
            <Items />
          </el-tab-pane>
          <template v-for="(item,index) in groupData" :key="index">
            <el-tab-pane :label="getGroupName(item)" :disabled="!item.isShow" :name="item.code" id="modelGroupTourStep">
              <!-- <ModelElemList /> -->
              <template #label>
                {{ getGroupName(item)}}
              </template>
              <component :groupname=item.code :is="currentComponent" />
            </el-tab-pane>
          </template>

        </TransitionGroup>
      </el-tabs>
    </KeepAlive>
  </div>
</template>

<script >
// import { defineComponent } from "vue";
// import { TabsPaneContext } from "element-plus";
// import { useI18n } from "vue-i18n";
// import { router } from "@/router";
import useCustomMadeStore from "@/store/custommade.js";
import ModelElemList from "./ModelElemList.vue";
import Items from './Items.vue'
import GroupMins from "@/mixins/group.js";
export default {
  name: "ModelGroupTabs",
  mixins: [GroupMins],
  components: {
    ModelElemList,
    Items
  },
  data() {
    const activeComponentsStore = useCustomMadeStore();
    return {
      currentComponent: "ModelElemList",
      activeComponentsStore: activeComponentsStore,
    };
  },
  computed: {
    groupData() {
      return this.activeComponentsStore.groupData;
    },
    isDataLoaded() {
      return this.activeComponentsStore.isDataLoaded;
    },
    isLoading() {
      return this.activeComponentsStore.isLoading;
    },
    activeName: {
      get() {
        return this.activeComponentsStore.activeGroup.code || "jacket";
      },
      set(value) {
        if (typeof value === "string") {
          const selectedGroup = this.groupData.find(item => item.code === value);
          if (selectedGroup) {
            this.activeComponentsStore.setActiveGroup(selectedGroup.code, selectedGroup.value, selectedGroup.modelID, selectedGroup.modelCode, selectedGroup.modelCodeName);
            history.replaceState({ groupID: selectedGroup.value }, '');
          }
        }
      }
    }
  },
  // watch: {
  //   isDataLoaded: {
  //     handler(newValue) {
  //       if (newValue && this.groupData.length > 0) {
  //         const currentGroupExists = this.groupData.some(
  //           (item) => item.code === this.activeName
  //         );
  //         if (!currentGroupExists) {
  //           this.activeName = this.groupData[0].code;
  //           this.activeComponentsStore.setActiveGroup(
  //             this.groupData[0].code,
  //             this.groupData[0].groupID,
  //             this.groupData[0].modelID,
  //             this.groupData[0].modelCode,
  //             this.groupData[0].modelCodeName,
  //           );
  //         }
  //       }
  //     },
  //     immediate: true,
  //   },
  // },
  methods: {
    handleClick(tab, _event) {
      if (typeof tab.paneName === "string") {
        const selectedGroup = this.groupData.find(item => item.code === tab.paneName);
        if (selectedGroup) {
          console.log(selectedGroup.modelCode)
          this.activeComponentsStore.setActiveGroup(selectedGroup.code, selectedGroup.value, selectedGroup.modelID, selectedGroup.modelCode, selectedGroup.modelCodeName);
          history.replaceState({ groupID: selectedGroup.value }, '');
        }
      }
    },
  },
};
</script>

<style lang="scss">
.modelgrouptabs {
  .el-tabs__content {
    height: 100%;
    .el-tab-pane {
      height: 100%;
    }
  }
}

.list-enter-active {
  transition: all 0.5s ease-out;
}

.list-leave-active {
  transition: all 0s cubic-bezier(1, 0.5, 0.8, 1);
}

.list-enter-from,
.list-leave-to {
  transform: translateX(500px);
  opacity: 0;
}

/* 确保将离开的元素从布局流中删除
    以便能够正确地计算移动的动画。 */
.list-leave-active {
  position: absolute;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
}

.loading-text {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}
</style>
